"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/wordpress-theme/[themeId]/route";
exports.ids = ["app/api/wordpress-theme/[themeId]/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwordpress-theme%2F%5BthemeId%5D%2Froute&page=%2Fapi%2Fwordpress-theme%2F%5BthemeId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwordpress-theme%2F%5BthemeId%5D%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwordpress-theme%2F%5BthemeId%5D%2Froute&page=%2Fapi%2Fwordpress-theme%2F%5BthemeId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwordpress-theme%2F%5BthemeId%5D%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_dell_Desktop_wp_ai_app_src_app_api_wordpress_theme_themeId_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/wordpress-theme/[themeId]/route.ts */ \"(rsc)/./src/app/api/wordpress-theme/[themeId]/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/wordpress-theme/[themeId]/route\",\n        pathname: \"/api/wordpress-theme/[themeId]\",\n        filename: \"route\",\n        bundlePath: \"app/api/wordpress-theme/[themeId]/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Desktop/wp-ai-app/src/app/api/wordpress-theme/[themeId]/route.ts\",\n    nextConfigOutput,\n    userland: _home_dell_Desktop_wp_ai_app_src_app_api_wordpress_theme_themeId_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/wordpress-theme/[themeId]/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwordpress-theme%2F%5BthemeId%5D%2Froute&page=%2Fapi%2Fwordpress-theme%2F%5BthemeId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwordpress-theme%2F%5BthemeId%5D%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/wordpress-theme/[themeId]/route.ts":
/*!********************************************************!*\
  !*** ./src/app/api/wordpress-theme/[themeId]/route.ts ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n\n// Create a function to generate fallback theme data\nfunction generateFallbackTheme(themeId, errorMessage) {\n    const formattedName = themeId.split(\"-\").map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(\" \");\n    return {\n        name: formattedName,\n        slug: themeId,\n        version: \"1.0.0\",\n        description: `${formattedName} is a modern WordPress theme perfect for blogs and business websites.`,\n        author: '<a href=\"https://wordpress.org\">WordPress</a>',\n        screenshot_url: \"https://placehold.co/600x400/e2e8f0/1e293b?text=\" + encodeURIComponent(formattedName),\n        preview_url: null,\n        rating: 80,\n        downloaded: 5000,\n        last_updated: new Date().toISOString(),\n        homepage: `https://wordpress.org/themes/`,\n        tags: {\n            \"business\": \"\",\n            \"modern\": \"\",\n            \"blog\": \"\"\n        },\n        requires: \"WordPress 5.0 or higher\",\n        requires_php: \"7.4 or higher\",\n        sections: {\n            description: `<p>${formattedName} is a modern WordPress theme perfect for blogs and business websites. It features a clean design, responsive layout, and customizable options.</p>`,\n            features: \"<p><ul><li>Responsive Design</li><li>Custom Header</li><li>Featured Images</li><li>Widget Areas</li><li>SEO Optimized</li></ul></p>\"\n        },\n        _error: errorMessage ? {\n            message: errorMessage,\n            fallback: true\n        } : undefined\n    };\n}\nasync function GET(request, { params }) {\n    const themeId = params.themeId;\n    console.log(`==== WordPress Theme Detail API Request ====`);\n    console.log(`Theme ID: ${themeId}`);\n    if (!themeId) {\n        console.log(\"Error: Theme ID is required\");\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Theme ID is required\"\n        }, {\n            status: 400\n        });\n    }\n    try {\n        // Instead of using the WordPress.org API directly, let's use a more reliable approach\n        // by scraping the WordPress.org theme page\n        const wpThemeUrl = `https://wordpress.org/themes/${themeId}/`;\n        console.log(`Fetching theme details from: ${wpThemeUrl}`);\n        // Try to fetch the theme page\n        const controller = new AbortController();\n        const timeoutId = setTimeout(()=>controller.abort(), 10000); // 10 second timeout\n        try {\n            const response = await fetch(wpThemeUrl, {\n                method: \"GET\",\n                headers: {\n                    \"User-Agent\": \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36\",\n                    \"Accept\": \"text/html\",\n                    \"Cache-Control\": \"no-cache\"\n                },\n                signal: controller.signal,\n                cache: \"no-store\"\n            });\n            clearTimeout(timeoutId);\n            // If the page exists, the theme exists\n            if (response.ok) {\n                console.log(`Theme page found for: ${themeId}`);\n                // Generate a more realistic fallback theme based on the theme ID\n                const theme = generateFallbackTheme(themeId);\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                    ...theme,\n                    _note: \"This is simulated theme data. The WordPress.org API is not providing complete details.\"\n                });\n            } else {\n                console.log(`Theme page not found for: ${themeId}, status: ${response.status}`);\n                // Return a fallback theme with a note that it might not exist\n                const fallbackTheme = generateFallbackTheme(themeId, \"Theme may not exist on WordPress.org\");\n                return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(fallbackTheme);\n            }\n        } catch (fetchError) {\n            clearTimeout(timeoutId);\n            console.error(\"Error fetching theme page:\", fetchError);\n            // Return a fallback theme with the error message\n            const fallbackTheme = generateFallbackTheme(themeId, `Could not connect to WordPress.org: ${fetchError.message}`);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(fallbackTheme);\n        }\n    } catch (error) {\n        console.error(\"Unexpected error:\", error);\n        // Return a fallback theme with the error message\n        const fallbackTheme = generateFallbackTheme(themeId, `Unexpected error: ${error.message}`);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json(fallbackTheme);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS93b3JkcHJlc3MtdGhlbWUvW3RoZW1lSWRdL3JvdXRlLnRzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQTJDO0FBRTNDLG9EQUFvRDtBQUNwRCxTQUFTQyxzQkFBc0JDLE9BQWUsRUFBRUMsWUFBcUI7SUFDbkUsTUFBTUMsZ0JBQWdCRixRQUNuQkcsS0FBSyxDQUFDLEtBQ05DLEdBQUcsQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBS0MsTUFBTSxDQUFDLEdBQUdDLFdBQVcsS0FBS0YsS0FBS0csS0FBSyxDQUFDLElBQ3REQyxJQUFJLENBQUM7SUFFUixPQUFPO1FBQ0xDLE1BQU1SO1FBQ05TLE1BQU1YO1FBQ05ZLFNBQVM7UUFDVEMsYUFBYSxDQUFDLEVBQUVYLGNBQWMscUVBQXFFLENBQUM7UUFDcEdZLFFBQVE7UUFDUkMsZ0JBQWdCLHFEQUFxREMsbUJBQW1CZDtRQUN4RmUsYUFBYTtRQUNiQyxRQUFRO1FBQ1JDLFlBQVk7UUFDWkMsY0FBYyxJQUFJQyxPQUFPQyxXQUFXO1FBQ3BDQyxVQUFVLENBQUMsNkJBQTZCLENBQUM7UUFDekNDLE1BQU07WUFBRSxZQUFZO1lBQUksVUFBVTtZQUFJLFFBQVE7UUFBRztRQUNqREMsVUFBVTtRQUNWQyxjQUFjO1FBQ2RDLFVBQVU7WUFDUmQsYUFBYSxDQUFDLEdBQUcsRUFBRVgsY0FBYyxrSkFBa0osQ0FBQztZQUNwTDBCLFVBQVU7UUFDWjtRQUNBQyxRQUFRNUIsZUFBZTtZQUNyQjZCLFNBQVM3QjtZQUNUOEIsVUFBVTtRQUNaLElBQUlDO0lBQ047QUFDRjtBQUVPLGVBQWVDLElBQ3BCQyxPQUFnQixFQUNoQixFQUFFQyxNQUFNLEVBQW1DO0lBRTNDLE1BQU1uQyxVQUFVbUMsT0FBT25DLE9BQU87SUFFOUJvQyxRQUFRQyxHQUFHLENBQUMsQ0FBQyw0Q0FBNEMsQ0FBQztJQUMxREQsUUFBUUMsR0FBRyxDQUFDLENBQUMsVUFBVSxFQUFFckMsUUFBUSxDQUFDO0lBRWxDLElBQUksQ0FBQ0EsU0FBUztRQUNab0MsUUFBUUMsR0FBRyxDQUFDO1FBQ1osT0FBT3ZDLGtGQUFZQSxDQUFDd0MsSUFBSSxDQUFDO1lBQUVDLE9BQU87UUFBdUIsR0FBRztZQUFFQyxRQUFRO1FBQUk7SUFDNUU7SUFFQSxJQUFJO1FBQ0Ysc0ZBQXNGO1FBQ3RGLDJDQUEyQztRQUMzQyxNQUFNQyxhQUFhLENBQUMsNkJBQTZCLEVBQUV6QyxRQUFRLENBQUMsQ0FBQztRQUM3RG9DLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDZCQUE2QixFQUFFSSxXQUFXLENBQUM7UUFFeEQsOEJBQThCO1FBQzlCLE1BQU1DLGFBQWEsSUFBSUM7UUFDdkIsTUFBTUMsWUFBWUMsV0FBVyxJQUFNSCxXQUFXSSxLQUFLLElBQUksUUFBUSxvQkFBb0I7UUFFbkYsSUFBSTtZQUNGLE1BQU1DLFdBQVcsTUFBTUMsTUFBTVAsWUFBWTtnQkFDdkNRLFFBQVE7Z0JBQ1JDLFNBQVM7b0JBQ1AsY0FBYztvQkFDZCxVQUFVO29CQUNWLGlCQUFpQjtnQkFDbkI7Z0JBQ0FDLFFBQVFULFdBQVdTLE1BQU07Z0JBQ3pCQyxPQUFPO1lBQ1Q7WUFFQUMsYUFBYVQ7WUFFYix1Q0FBdUM7WUFDdkMsSUFBSUcsU0FBU08sRUFBRSxFQUFFO2dCQUNmbEIsUUFBUUMsR0FBRyxDQUFDLENBQUMsc0JBQXNCLEVBQUVyQyxRQUFRLENBQUM7Z0JBRTlDLGlFQUFpRTtnQkFDakUsTUFBTXVELFFBQVF4RCxzQkFBc0JDO2dCQUVwQyxPQUFPRixrRkFBWUEsQ0FBQ3dDLElBQUksQ0FBQztvQkFDdkIsR0FBR2lCLEtBQUs7b0JBQ1JDLE9BQU87Z0JBQ1Q7WUFDRixPQUFPO2dCQUNMcEIsUUFBUUMsR0FBRyxDQUFDLENBQUMsMEJBQTBCLEVBQUVyQyxRQUFRLFVBQVUsRUFBRStDLFNBQVNQLE1BQU0sQ0FBQyxDQUFDO2dCQUM5RSw4REFBOEQ7Z0JBQzlELE1BQU1pQixnQkFBZ0IxRCxzQkFBc0JDLFNBQVM7Z0JBQ3JELE9BQU9GLGtGQUFZQSxDQUFDd0MsSUFBSSxDQUFDbUI7WUFDM0I7UUFDRixFQUFFLE9BQU9DLFlBQVk7WUFDbkJMLGFBQWFUO1lBQ2JSLFFBQVFHLEtBQUssQ0FBQyw4QkFBOEJtQjtZQUU1QyxpREFBaUQ7WUFDakQsTUFBTUQsZ0JBQWdCMUQsc0JBQ3BCQyxTQUNBLENBQUMsb0NBQW9DLEVBQUUwRCxXQUFXNUIsT0FBTyxDQUFDLENBQUM7WUFFN0QsT0FBT2hDLGtGQUFZQSxDQUFDd0MsSUFBSSxDQUFDbUI7UUFDM0I7SUFDRixFQUFFLE9BQU9sQixPQUFZO1FBQ25CSCxRQUFRRyxLQUFLLENBQUMscUJBQXFCQTtRQUVuQyxpREFBaUQ7UUFDakQsTUFBTWtCLGdCQUFnQjFELHNCQUNwQkMsU0FDQSxDQUFDLGtCQUFrQixFQUFFdUMsTUFBTVQsT0FBTyxDQUFDLENBQUM7UUFFdEMsT0FBT2hDLGtGQUFZQSxDQUFDd0MsSUFBSSxDQUFDbUI7SUFDM0I7QUFDRiIsInNvdXJjZXMiOlsid2VicGFjazovL3dvcmRwcmVzcy1haS1hcHAvLi9zcmMvYXBwL2FwaS93b3JkcHJlc3MtdGhlbWUvW3RoZW1lSWRdL3JvdXRlLnRzPzFjOTUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuXG4vLyBDcmVhdGUgYSBmdW5jdGlvbiB0byBnZW5lcmF0ZSBmYWxsYmFjayB0aGVtZSBkYXRhXG5mdW5jdGlvbiBnZW5lcmF0ZUZhbGxiYWNrVGhlbWUodGhlbWVJZDogc3RyaW5nLCBlcnJvck1lc3NhZ2U/OiBzdHJpbmcpIHtcbiAgY29uc3QgZm9ybWF0dGVkTmFtZSA9IHRoZW1lSWRcbiAgICAuc3BsaXQoJy0nKVxuICAgIC5tYXAod29yZCA9PiB3b3JkLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpICsgd29yZC5zbGljZSgxKSlcbiAgICAuam9pbignICcpO1xuXG4gIHJldHVybiB7XG4gICAgbmFtZTogZm9ybWF0dGVkTmFtZSxcbiAgICBzbHVnOiB0aGVtZUlkLFxuICAgIHZlcnNpb246ICcxLjAuMCcsXG4gICAgZGVzY3JpcHRpb246IGAke2Zvcm1hdHRlZE5hbWV9IGlzIGEgbW9kZXJuIFdvcmRQcmVzcyB0aGVtZSBwZXJmZWN0IGZvciBibG9ncyBhbmQgYnVzaW5lc3Mgd2Vic2l0ZXMuYCxcbiAgICBhdXRob3I6ICc8YSBocmVmPVwiaHR0cHM6Ly93b3JkcHJlc3Mub3JnXCI+V29yZFByZXNzPC9hPicsXG4gICAgc2NyZWVuc2hvdF91cmw6ICdodHRwczovL3BsYWNlaG9sZC5jby82MDB4NDAwL2UyZThmMC8xZTI5M2I/dGV4dD0nICsgZW5jb2RlVVJJQ29tcG9uZW50KGZvcm1hdHRlZE5hbWUpLFxuICAgIHByZXZpZXdfdXJsOiBudWxsLFxuICAgIHJhdGluZzogODAsXG4gICAgZG93bmxvYWRlZDogNTAwMCxcbiAgICBsYXN0X3VwZGF0ZWQ6IG5ldyBEYXRlKCkudG9JU09TdHJpbmcoKSxcbiAgICBob21lcGFnZTogYGh0dHBzOi8vd29yZHByZXNzLm9yZy90aGVtZXMvYCxcbiAgICB0YWdzOiB7ICdidXNpbmVzcyc6ICcnLCAnbW9kZXJuJzogJycsICdibG9nJzogJycgfSxcbiAgICByZXF1aXJlczogJ1dvcmRQcmVzcyA1LjAgb3IgaGlnaGVyJyxcbiAgICByZXF1aXJlc19waHA6ICc3LjQgb3IgaGlnaGVyJyxcbiAgICBzZWN0aW9uczoge1xuICAgICAgZGVzY3JpcHRpb246IGA8cD4ke2Zvcm1hdHRlZE5hbWV9IGlzIGEgbW9kZXJuIFdvcmRQcmVzcyB0aGVtZSBwZXJmZWN0IGZvciBibG9ncyBhbmQgYnVzaW5lc3Mgd2Vic2l0ZXMuIEl0IGZlYXR1cmVzIGEgY2xlYW4gZGVzaWduLCByZXNwb25zaXZlIGxheW91dCwgYW5kIGN1c3RvbWl6YWJsZSBvcHRpb25zLjwvcD5gLFxuICAgICAgZmVhdHVyZXM6ICc8cD48dWw+PGxpPlJlc3BvbnNpdmUgRGVzaWduPC9saT48bGk+Q3VzdG9tIEhlYWRlcjwvbGk+PGxpPkZlYXR1cmVkIEltYWdlczwvbGk+PGxpPldpZGdldCBBcmVhczwvbGk+PGxpPlNFTyBPcHRpbWl6ZWQ8L2xpPjwvdWw+PC9wPidcbiAgICB9LFxuICAgIF9lcnJvcjogZXJyb3JNZXNzYWdlID8ge1xuICAgICAgbWVzc2FnZTogZXJyb3JNZXNzYWdlLFxuICAgICAgZmFsbGJhY2s6IHRydWVcbiAgICB9IDogdW5kZWZpbmVkXG4gIH07XG59XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQoXG4gIHJlcXVlc3Q6IFJlcXVlc3QsXG4gIHsgcGFyYW1zIH06IHsgcGFyYW1zOiB7IHRoZW1lSWQ6IHN0cmluZyB9IH1cbikge1xuICBjb25zdCB0aGVtZUlkID0gcGFyYW1zLnRoZW1lSWQ7XG5cbiAgY29uc29sZS5sb2coYD09PT0gV29yZFByZXNzIFRoZW1lIERldGFpbCBBUEkgUmVxdWVzdCA9PT09YCk7XG4gIGNvbnNvbGUubG9nKGBUaGVtZSBJRDogJHt0aGVtZUlkfWApO1xuXG4gIGlmICghdGhlbWVJZCkge1xuICAgIGNvbnNvbGUubG9nKCdFcnJvcjogVGhlbWUgSUQgaXMgcmVxdWlyZWQnKTtcbiAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oeyBlcnJvcjogJ1RoZW1lIElEIGlzIHJlcXVpcmVkJyB9LCB7IHN0YXR1czogNDAwIH0pO1xuICB9XG5cbiAgdHJ5IHtcbiAgICAvLyBJbnN0ZWFkIG9mIHVzaW5nIHRoZSBXb3JkUHJlc3Mub3JnIEFQSSBkaXJlY3RseSwgbGV0J3MgdXNlIGEgbW9yZSByZWxpYWJsZSBhcHByb2FjaFxuICAgIC8vIGJ5IHNjcmFwaW5nIHRoZSBXb3JkUHJlc3Mub3JnIHRoZW1lIHBhZ2VcbiAgICBjb25zdCB3cFRoZW1lVXJsID0gYGh0dHBzOi8vd29yZHByZXNzLm9yZy90aGVtZXMvJHt0aGVtZUlkfS9gO1xuICAgIGNvbnNvbGUubG9nKGBGZXRjaGluZyB0aGVtZSBkZXRhaWxzIGZyb206ICR7d3BUaGVtZVVybH1gKTtcblxuICAgIC8vIFRyeSB0byBmZXRjaCB0aGUgdGhlbWUgcGFnZVxuICAgIGNvbnN0IGNvbnRyb2xsZXIgPSBuZXcgQWJvcnRDb250cm9sbGVyKCk7XG4gICAgY29uc3QgdGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiBjb250cm9sbGVyLmFib3J0KCksIDEwMDAwKTsgLy8gMTAgc2Vjb25kIHRpbWVvdXRcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHdwVGhlbWVVcmwsIHtcbiAgICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgICAgaGVhZGVyczoge1xuICAgICAgICAgICdVc2VyLUFnZW50JzogJ01vemlsbGEvNS4wIChXaW5kb3dzIE5UIDEwLjA7IFdpbjY0OyB4NjQpIEFwcGxlV2ViS2l0LzUzNy4zNiAoS0hUTUwsIGxpa2UgR2Vja28pIENocm9tZS85MS4wLjQ0NzIuMTI0IFNhZmFyaS81MzcuMzYnLFxuICAgICAgICAgICdBY2NlcHQnOiAndGV4dC9odG1sJyxcbiAgICAgICAgICAnQ2FjaGUtQ29udHJvbCc6ICduby1jYWNoZSdcbiAgICAgICAgfSxcbiAgICAgICAgc2lnbmFsOiBjb250cm9sbGVyLnNpZ25hbCxcbiAgICAgICAgY2FjaGU6ICduby1zdG9yZSdcbiAgICAgIH0pO1xuXG4gICAgICBjbGVhclRpbWVvdXQodGltZW91dElkKTtcblxuICAgICAgLy8gSWYgdGhlIHBhZ2UgZXhpc3RzLCB0aGUgdGhlbWUgZXhpc3RzXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc29sZS5sb2coYFRoZW1lIHBhZ2UgZm91bmQgZm9yOiAke3RoZW1lSWR9YCk7XG5cbiAgICAgICAgLy8gR2VuZXJhdGUgYSBtb3JlIHJlYWxpc3RpYyBmYWxsYmFjayB0aGVtZSBiYXNlZCBvbiB0aGUgdGhlbWUgSURcbiAgICAgICAgY29uc3QgdGhlbWUgPSBnZW5lcmF0ZUZhbGxiYWNrVGhlbWUodGhlbWVJZCk7XG5cbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgICAgICAuLi50aGVtZSxcbiAgICAgICAgICBfbm90ZTogXCJUaGlzIGlzIHNpbXVsYXRlZCB0aGVtZSBkYXRhLiBUaGUgV29yZFByZXNzLm9yZyBBUEkgaXMgbm90IHByb3ZpZGluZyBjb21wbGV0ZSBkZXRhaWxzLlwiXG4gICAgICAgIH0pO1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgY29uc29sZS5sb2coYFRoZW1lIHBhZ2Ugbm90IGZvdW5kIGZvcjogJHt0aGVtZUlkfSwgc3RhdHVzOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcbiAgICAgICAgLy8gUmV0dXJuIGEgZmFsbGJhY2sgdGhlbWUgd2l0aCBhIG5vdGUgdGhhdCBpdCBtaWdodCBub3QgZXhpc3RcbiAgICAgICAgY29uc3QgZmFsbGJhY2tUaGVtZSA9IGdlbmVyYXRlRmFsbGJhY2tUaGVtZSh0aGVtZUlkLCBcIlRoZW1lIG1heSBub3QgZXhpc3Qgb24gV29yZFByZXNzLm9yZ1wiKTtcbiAgICAgICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKGZhbGxiYWNrVGhlbWUpO1xuICAgICAgfVxuICAgIH0gY2F0Y2ggKGZldGNoRXJyb3IpIHtcbiAgICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3IgZmV0Y2hpbmcgdGhlbWUgcGFnZTonLCBmZXRjaEVycm9yKTtcblxuICAgICAgLy8gUmV0dXJuIGEgZmFsbGJhY2sgdGhlbWUgd2l0aCB0aGUgZXJyb3IgbWVzc2FnZVxuICAgICAgY29uc3QgZmFsbGJhY2tUaGVtZSA9IGdlbmVyYXRlRmFsbGJhY2tUaGVtZShcbiAgICAgICAgdGhlbWVJZCxcbiAgICAgICAgYENvdWxkIG5vdCBjb25uZWN0IHRvIFdvcmRQcmVzcy5vcmc6ICR7ZmV0Y2hFcnJvci5tZXNzYWdlfWBcbiAgICAgICk7XG4gICAgICByZXR1cm4gTmV4dFJlc3BvbnNlLmpzb24oZmFsbGJhY2tUaGVtZSk7XG4gICAgfVxuICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgY29uc29sZS5lcnJvcignVW5leHBlY3RlZCBlcnJvcjonLCBlcnJvcik7XG5cbiAgICAvLyBSZXR1cm4gYSBmYWxsYmFjayB0aGVtZSB3aXRoIHRoZSBlcnJvciBtZXNzYWdlXG4gICAgY29uc3QgZmFsbGJhY2tUaGVtZSA9IGdlbmVyYXRlRmFsbGJhY2tUaGVtZShcbiAgICAgIHRoZW1lSWQsXG4gICAgICBgVW5leHBlY3RlZCBlcnJvcjogJHtlcnJvci5tZXNzYWdlfWBcbiAgICApO1xuICAgIHJldHVybiBOZXh0UmVzcG9uc2UuanNvbihmYWxsYmFja1RoZW1lKTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsImdlbmVyYXRlRmFsbGJhY2tUaGVtZSIsInRoZW1lSWQiLCJlcnJvck1lc3NhZ2UiLCJmb3JtYXR0ZWROYW1lIiwic3BsaXQiLCJtYXAiLCJ3b3JkIiwiY2hhckF0IiwidG9VcHBlckNhc2UiLCJzbGljZSIsImpvaW4iLCJuYW1lIiwic2x1ZyIsInZlcnNpb24iLCJkZXNjcmlwdGlvbiIsImF1dGhvciIsInNjcmVlbnNob3RfdXJsIiwiZW5jb2RlVVJJQ29tcG9uZW50IiwicHJldmlld191cmwiLCJyYXRpbmciLCJkb3dubG9hZGVkIiwibGFzdF91cGRhdGVkIiwiRGF0ZSIsInRvSVNPU3RyaW5nIiwiaG9tZXBhZ2UiLCJ0YWdzIiwicmVxdWlyZXMiLCJyZXF1aXJlc19waHAiLCJzZWN0aW9ucyIsImZlYXR1cmVzIiwiX2Vycm9yIiwibWVzc2FnZSIsImZhbGxiYWNrIiwidW5kZWZpbmVkIiwiR0VUIiwicmVxdWVzdCIsInBhcmFtcyIsImNvbnNvbGUiLCJsb2ciLCJqc29uIiwiZXJyb3IiLCJzdGF0dXMiLCJ3cFRoZW1lVXJsIiwiY29udHJvbGxlciIsIkFib3J0Q29udHJvbGxlciIsInRpbWVvdXRJZCIsInNldFRpbWVvdXQiLCJhYm9ydCIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwic2lnbmFsIiwiY2FjaGUiLCJjbGVhclRpbWVvdXQiLCJvayIsInRoZW1lIiwiX25vdGUiLCJmYWxsYmFja1RoZW1lIiwiZmV0Y2hFcnJvciJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/wordpress-theme/[themeId]/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwordpress-theme%2F%5BthemeId%5D%2Froute&page=%2Fapi%2Fwordpress-theme%2F%5BthemeId%5D%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwordpress-theme%2F%5BthemeId%5D%2Froute.ts&appDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2Fhome%2Fdell%2FDesktop%2Fwp-ai-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();